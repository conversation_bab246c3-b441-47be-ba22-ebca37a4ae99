namespace VideoMessageTimeSearcherApi.DTOs
{
    public class CreateVideoRequest
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Duration { get; set; } // Changed from DurationSeconds to match UI
        public string Transcript { get; set; } = string.Empty;
        public List<TranscriptSegmentRequest> TranscriptSegments { get; set; } = new();
        public string FilePath { get; set; } = string.Empty;
    }

    public class TranscriptSegmentRequest
    {
        public string Text { get; set; } = string.Empty;
        public double StartTime { get; set; }
        public double EndTime { get; set; }
    }
}
