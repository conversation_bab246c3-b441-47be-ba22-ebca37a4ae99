import apiClient from './apiClient';

export interface TranscriptSegment {
  text: string;
  startTime: number; // in seconds
  endTime: number; // in seconds
}

export interface CreateVideoRequest {
  title: string;
  description: string;
  duration: number; // in seconds
  transcript: string;
  transcriptSegments: TranscriptSegment[];
}

export const createVideo = async (videoData: CreateVideoRequest) => {
  const response = await apiClient.post('/videos', videoData);
  return response.data;
};
