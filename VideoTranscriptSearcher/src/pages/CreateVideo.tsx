import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiClock, FiSave, FiArrowLeft, FiMove } from 'react-icons/fi';
import apiClient from '../api/apiClient';

interface WordTiming {
  word: string;
  startTime: number; // in seconds
  endTime: number; // in seconds
}

interface TranscriptSegment {
  id: string;
  text: string;
  startTime: number; // in seconds
  endTime: number; // in seconds
  words?: WordTiming[];
}

const CreateVideo = () => {
  const navigate = useNavigate();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [duration, setDuration] = useState<number>(0);
  const [durationMinutes, setDurationMinutes] = useState<string>('');
  const [durationSeconds, setDurationSeconds] = useState<string>('');
  const [fullTranscript, setFullTranscript] = useState('');
  const [segments, setSegments] = useState<TranscriptSegment[]>([]);
  const [currentSegment, setCurrentSegment] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragType, setDragType] = useState<'start' | 'end' | null>(null);
  const [dragSegmentIndex, setDragSegmentIndex] = useState<number | null>(null);
  const [showWordTimeline, setShowWordTimeline] = useState<number | null>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const segmentRefs = useRef<(HTMLDivElement | null)[]>([]);
  const wordTimelineRef = useRef<HTMLDivElement>(null);

  // Update total duration when minutes or seconds change
  useEffect(() => {
    const mins = parseInt(durationMinutes) || 0;
    const secs = parseInt(durationSeconds) || 0;
    setDuration(mins * 60 + secs);
  }, [durationMinutes, durationSeconds]);

  // Process transcript into segments when full transcript changes
  const processTranscript = () => {
    if (!fullTranscript.trim()) {
      setSegments([]);
      return;
    }

    // Split by sentences (simple implementation - split by periods, question marks, exclamation points)
    const sentences = fullTranscript
      .split(/(?<=[.!?])\s+/)
      .filter(sentence => sentence.trim().length > 0);

    // Create initial segments with evenly distributed times
    const totalDuration = duration;
    const segmentDuration = totalDuration / sentences.length;

    const newSegments = sentences.map((sentence, index) => {
      const startTime = Math.round(index * segmentDuration);
      const endTime = Math.round((index + 1) * segmentDuration);
      
      // Process words within the segment
      const words = sentence.trim().split(/\s+/).filter(word => word.length > 0);
      const wordDuration = (endTime - startTime) / words.length;
      
      const wordTimings: WordTiming[] = words.map((word, wordIndex) => {
        const wordStartTime = startTime + (wordIndex * wordDuration);
        const wordEndTime = wordIndex === words.length - 1 
          ? endTime 
          : startTime + ((wordIndex + 1) * wordDuration);
        
        return {
          word,
          startTime: parseFloat(wordStartTime.toFixed(2)),
          endTime: parseFloat(wordEndTime.toFixed(2))
        };
      });
      
      return {
        id: `segment-${index}`,
        text: sentence.trim(),
        startTime,
        endTime,
        words: wordTimings
      };
    });

    setSegments(newSegments);
  };

  // Format seconds to MM:SS format
  const formatTime = (timeInSeconds: number): string => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Update segment time
  const updateSegmentTime = (index: number, isStart: boolean, newTimeInSeconds: number) => {
    setSegments(prevSegments => {
      const newSegments = [...prevSegments];
      const segment = {...newSegments[index]};
      const oldStartTime = segment.startTime;
      const oldEndTime = segment.endTime;
      const oldDuration = oldEndTime - oldStartTime;
      
      if (isStart) {
        // Ensure start time doesn't exceed end time
        segment.startTime = Math.min(newTimeInSeconds, segment.endTime - 1);
        
        // If not the first segment, ensure it doesn't overlap with previous segment
        if (index > 0) {
          segment.startTime = Math.max(segment.startTime, newSegments[index - 1].endTime);
        }
      } else {
        // Ensure end time is greater than start time
        segment.endTime = Math.max(newTimeInSeconds, segment.startTime + 1);
        
        // If not the last segment, ensure it doesn't overlap with next segment
        if (index < newSegments.length - 1) {
          segment.endTime = Math.min(segment.endTime, newSegments[index + 1].startTime);
        }
      }
      
      // Update word timings proportionally
      if (segment.words && segment.words.length > 0) {
        const newDuration = segment.endTime - segment.startTime;
        const ratio = newDuration / oldDuration;
        
        segment.words = segment.words.map((word, wordIndex) => {
          // Calculate relative positions for each word
          const wordRelativeStart = word.startTime - oldStartTime;
          const wordRelativeEnd = word.endTime - oldStartTime;
          
          const newWordStart = segment.startTime + (wordRelativeStart * ratio);
          const newWordEnd = wordIndex === segment.words!.length - 1 
            ? segment.endTime 
            : segment.startTime + (wordRelativeEnd * ratio);
          
          return {
            ...word,
            startTime: parseFloat(newWordStart.toFixed(2)),
            endTime: parseFloat(newWordEnd.toFixed(2))
          };
        });
      }
      
      newSegments[index] = segment;
      return newSegments;
    });
  };
  
  // Handle dragging segment markers
  const handleTimelineDragStart = (_e: React.MouseEvent, index: number, type: 'start' | 'end') => {
    setIsDragging(true);
    setDragType(type);
    setDragSegmentIndex(index);
    document.addEventListener('mousemove', handleTimelineDragMove);
    document.addEventListener('mouseup', handleTimelineDragEnd);
  };
  
  const handleTimelineDragMove = (e: MouseEvent) => {
    if (!isDragging || dragSegmentIndex === null || !timelineRef.current || dragType === null) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const position = (e.clientX - rect.left) / rect.width;
    const timeInSeconds = Math.max(0, Math.min(Math.round(position * duration), duration));
    
    updateSegmentTime(dragSegmentIndex, dragType === 'start', timeInSeconds);
  };
  
  const handleTimelineDragEnd = () => {
    setIsDragging(false);
    setDragType(null);
    setDragSegmentIndex(null);
    document.removeEventListener('mousemove', handleTimelineDragMove);
    document.removeEventListener('mouseup', handleTimelineDragEnd);
  };
  
  // Update word timing
  const updateWordTiming = (segmentIndex: number, wordIndex: number, isStart: boolean, newTimeInSeconds: number) => {
    setSegments(prevSegments => {
      const newSegments = [...prevSegments];
      const segment = {...newSegments[segmentIndex]};
      
      if (!segment.words) return newSegments;
      
      const words = [...segment.words];
      const word = {...words[wordIndex]};
      
      if (isStart) {
        // Ensure word start time is within bounds
        word.startTime = Math.max(segment.startTime, newTimeInSeconds);
        word.startTime = Math.min(word.startTime, word.endTime - 0.1);
        
        // Don't overlap with previous word
        if (wordIndex > 0) {
          word.startTime = Math.max(word.startTime, words[wordIndex - 1].endTime);
        }
      } else {
        // Ensure word end time is within bounds
        word.endTime = Math.min(segment.endTime, newTimeInSeconds);
        word.endTime = Math.max(word.endTime, word.startTime + 0.1);
        
        // Don't overlap with next word
        if (wordIndex < words.length - 1) {
          word.endTime = Math.min(word.endTime, words[wordIndex + 1].startTime);
        }
      }
      
      words[wordIndex] = word;
      segment.words = words;
      newSegments[segmentIndex] = segment;
      
      return newSegments;
    });
  };
  
  // Handle dragging word markers
  const handleWordDragStart = (e: React.MouseEvent, segmentIndex: number, wordIndex: number, type: 'start' | 'end') => {
    e.stopPropagation();
    setIsDragging(true);
    setDragType(type);
    setDragSegmentIndex(segmentIndex);
    const handleWordDragMove = (e: MouseEvent) => {
      if (!isDragging || !wordTimelineRef.current) return;
      
      const rect = wordTimelineRef.current.getBoundingClientRect();
      const position = (e.clientX - rect.left) / rect.width;
      const segment = segments[segmentIndex];
      const segmentDuration = segment.endTime - segment.startTime;
      const timeInSeconds = segment.startTime + Math.max(0, Math.min(position * segmentDuration, segmentDuration));
      
      updateWordTiming(segmentIndex, wordIndex, type === 'start', parseFloat(timeInSeconds.toFixed(2)));
    };
    
    const handleWordDragEnd = () => {
      setIsDragging(false);
      setDragType(null);
      setDragSegmentIndex(null);
      document.removeEventListener('mousemove', handleWordDragMove);
      document.removeEventListener('mouseup', handleWordDragEnd);
    };
    
    document.addEventListener('mousemove', handleWordDragMove);
    document.addEventListener('mouseup', handleWordDragEnd);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) {
      setError('Title is required');
      return;
    }
    
    if (duration <= 0) {
      setError('Valid duration is required');
      return;
    }
    
    if (segments.length === 0) {
      setError('Transcript is required');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Prepare transcript data with timestamps
      const transcriptWithTimestamps = segments.map(segment => ({
        text: segment.text,
        startTime: segment.startTime,
        endTime: segment.endTime
      }));
      
      // Create video data object
      const videoData = {
        title,
        description,
        duration,
        transcript: fullTranscript,
        transcriptSegments: transcriptWithTimestamps
      };
      
      // Send to API
      await apiClient.post('/videos', videoData);
      
      // Redirect to home page on success
      navigate('/');
    } catch (err) {
      console.error('Error creating video:', err);
      setError('Failed to create video. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate('/')}
          className="mr-4 p-2 rounded-full text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          aria-label="Back to home"
        >
          <FiArrowLeft className="h-5 w-5" />
        </button>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add New Video</h1>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Video Details Section */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Video Details</h2>
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Title *
                </label>
                <input
                  type="text"
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter video title"
                  required
                />
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter video description"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Duration *
                </label>
                <div className="flex items-center">
                  <div className="flex items-center">
                    <input
                      type="number"
                      value={durationMinutes}
                      onChange={(e) => setDurationMinutes(e.target.value)}
                      min="0"
                      className="w-20 p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Min"
                    />
                    <span className="mx-2 text-gray-700 dark:text-gray-300">min</span>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="number"
                      value={durationSeconds}
                      onChange={(e) => setDurationSeconds(e.target.value)}
                      min="0"
                      max="59"
                      className="w-20 p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Sec"
                    />
                    <span className="mx-2 text-gray-700 dark:text-gray-300">sec</span>
                  </div>
                  <div className="ml-2 text-gray-500 dark:text-gray-400 flex items-center">
                    <FiClock className="h-5 w-5 mr-1" />
                    <span>Total: {formatTime(duration)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Transcript Section */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Transcript</h2>
            <div className="space-y-4">
              <div>
                <label htmlFor="transcript" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Full Transcript *
                </label>
                <textarea
                  id="transcript"
                  value={fullTranscript}
                  onChange={(e) => setFullTranscript(e.target.value)}
                  rows={6}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Paste the full video transcript here"
                />
              </div>

              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={processTranscript}
                  disabled={!fullTranscript.trim() || duration <= 0}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Process Transcript
                </button>
              </div>
            </div>
          </div>

          {/* Timeline Editor Section */}
          {segments.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Timeline Editor</h2>
              
              {/* Timeline visualization */}
              <div 
                ref={timelineRef}
                className="relative h-12 bg-gray-200 dark:bg-gray-700 rounded-md mb-6"
              >
                {/* Time markers */}
                {Array.from({ length: Math.ceil(duration / 60) + 1 }).map((_, i) => (
                  <div 
                    key={`marker-${i}`}
                    className="absolute top-0 h-full border-l border-gray-400 dark:border-gray-500"
                    style={{ left: `${(i * 60 / duration) * 100}%` }}
                  >
                    <div className="absolute -top-6 -translate-x-1/2 text-xs text-gray-600 dark:text-gray-400">
                      {formatTime(i * 60)}
                    </div>
                  </div>
                ))}
                
                {/* Segment visualizations */}
                {segments.map((segment, index) => (
                  <div
                    key={segment.id}
                    ref={(el) => { segmentRefs.current[index] = el; return undefined; }}
                    className={`absolute h-full rounded-md cursor-pointer group ${
                      currentSegment === index 
                        ? 'bg-blue-500' 
                        : 'bg-blue-300 dark:bg-blue-700'
                    }`}
                    style={{
                      left: `${(segment.startTime / duration) * 100}%`,
                      width: `${((segment.endTime - segment.startTime) / duration) * 100}%`,
                    }}
                    onClick={() => {
                      setCurrentSegment(index);
                      setShowWordTimeline(showWordTimeline === index ? null : index);
                    }}
                  >
                    {/* Draggable start handle */}
                    <div 
                      className="absolute left-0 top-0 h-full w-2 bg-blue-800 opacity-0 group-hover:opacity-100 cursor-ew-resize"
                      onMouseDown={(e) => handleTimelineDragStart(e, index, 'start')}
                      title="Drag to adjust start time"
                    >
                      <FiMove className="absolute -left-1 top-1/2 -translate-y-1/2 text-white" size={12} />
                    </div>
                    
                    {/* Draggable end handle */}
                    <div 
                      className="absolute right-0 top-0 h-full w-2 bg-blue-800 opacity-0 group-hover:opacity-100 cursor-ew-resize"
                      onMouseDown={(e) => handleTimelineDragStart(e, index, 'end')}
                      title="Drag to adjust end time"
                    >
                      <FiMove className="absolute -right-1 top-1/2 -translate-y-1/2 text-white" size={12} />
                    </div>
                    
                    {/* Segment time label */}
                    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-xs text-white font-medium">
                      {formatTime(segment.startTime)} - {formatTime(segment.endTime)}
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Word-level timeline for selected segment */}
              {showWordTimeline !== null && segments[showWordTimeline]?.words && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Word Timeline for Segment {showWordTimeline + 1}
                  </h3>
                  <div 
                    ref={wordTimelineRef}
                    className="relative h-8 bg-gray-300 dark:bg-gray-600 rounded-md mb-2"
                  >
                    {/* Word visualizations */}
                    {segments[showWordTimeline].words?.map((word, wordIndex) => {
                      const segment = segments[showWordTimeline];
                      const segmentDuration = segment.endTime - segment.startTime;
                      const relativeStart = (word.startTime - segment.startTime) / segmentDuration;
                      const relativeWidth = (word.endTime - word.startTime) / segmentDuration;
                      
                      return (
                        <div
                          key={`${segment.id}-word-${wordIndex}`}
                          className="absolute h-full bg-green-400 dark:bg-green-600 rounded-sm group"
                          style={{
                            left: `${relativeStart * 100}%`,
                            width: `${relativeWidth * 100}%`,
                          }}
                        >
                          {/* Draggable start handle */}
                          <div 
                            className="absolute left-0 top-0 h-full w-1 bg-green-800 opacity-0 group-hover:opacity-100 cursor-ew-resize"
                            onMouseDown={(e) => handleWordDragStart(e, showWordTimeline, wordIndex, 'start')}
                            title="Drag to adjust word start time"
                          />
                          
                          {/* Draggable end handle */}
                          <div 
                            className="absolute right-0 top-0 h-full w-1 bg-green-800 opacity-0 group-hover:opacity-100 cursor-ew-resize"
                            onMouseDown={(e) => handleWordDragStart(e, showWordTimeline, wordIndex, 'end')}
                            title="Drag to adjust word end time"
                          />
                          
                          {/* Word label (only show if enough space) */}
                          {relativeWidth > 0.05 && (
                            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-xs text-white font-medium whitespace-nowrap overflow-hidden text-ellipsis max-w-full px-1">
                              {word.word}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                  
                  {/* Word timing controls */}
                  <div className="grid grid-cols-4 gap-2 text-sm">
                    {segments[showWordTimeline].words?.map((word, wordIndex) => (
                      <div key={`${segments[showWordTimeline].id}-word-control-${wordIndex}`} className="flex flex-col p-2 border border-gray-200 dark:border-gray-700 rounded">
                        <div className="font-medium mb-1">{word.word}</div>
                        <div className="flex justify-between gap-2">
                          <div className="flex items-center">
                            <span className="text-xs text-gray-600 dark:text-gray-400 mr-1">Start:</span>
                            <input
                              type="number"
                              value={word.startTime}
                              onChange={(e) => updateWordTiming(showWordTimeline, wordIndex, true, parseFloat(e.target.value) || 0)}
                              step="0.1"
                              min={wordIndex > 0 ? segments[showWordTimeline].words![wordIndex - 1].endTime : segments[showWordTimeline].startTime}
                              max={word.endTime - 0.1}
                              className="w-16 p-1 text-xs border border-gray-300 dark:border-gray-600 rounded"
                            />
                          </div>
                          <div className="flex items-center">
                            <span className="text-xs text-gray-600 dark:text-gray-400 mr-1">End:</span>
                            <input
                              type="number"
                              value={word.endTime}
                              onChange={(e) => updateWordTiming(showWordTimeline, wordIndex, false, parseFloat(e.target.value) || 0)}
                              step="0.1"
                              min={word.startTime + 0.1}
                              max={wordIndex < segments[showWordTimeline].words!.length - 1 ? segments[showWordTimeline].words![wordIndex + 1].startTime : segments[showWordTimeline].endTime}
                              className="w-16 p-1 text-xs border border-gray-300 dark:border-gray-600 rounded"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Segment editor */}
              <div className="space-y-4">
                {segments.map((segment, index) => (
                  <div 
                    key={segment.id}
                    className={`p-4 border rounded-md ${
                      currentSegment === index 
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                        : 'border-gray-300 dark:border-gray-600'
                    }`}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div className="font-medium text-gray-800 dark:text-gray-200">
                        Segment {index + 1}
                      </div>
                      <div className="flex space-x-4">
                        <div className="flex items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400 mr-2">Start:</span>
                          <input
                            type="number"
                            value={segment.startTime}
                            onChange={(e) => updateSegmentTime(index, true, parseInt(e.target.value) || 0)}
                            min={index > 0 ? segments[index - 1].endTime : 0}
                            max={segment.endTime - 1}
                            className="w-16 p-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                          />
                          <span className="ml-1 text-sm text-gray-600 dark:text-gray-400">sec</span>
                        </div>
                        <div className="flex items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400 mr-2">End:</span>
                          <input
                            type="number"
                            value={segment.endTime}
                            onChange={(e) => updateSegmentTime(index, false, parseInt(e.target.value) || 0)}
                            min={segment.startTime + 1}
                            max={index < segments.length - 1 ? segments[index + 1].startTime : duration}
                            className="w-16 p-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                          />
                          <span className="ml-1 text-sm text-gray-600 dark:text-gray-400">sec</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-gray-700 dark:text-gray-300">
                      {segment.text}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Submit button */}
          <div className="flex justify-end pt-4">
            <button
              type="submit"
              disabled={isSubmitting || segments.length === 0}
              className="flex items-center px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FiSave className="mr-2" />
              {isSubmitting ? 'Saving...' : 'Save Video'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CreateVideo;
